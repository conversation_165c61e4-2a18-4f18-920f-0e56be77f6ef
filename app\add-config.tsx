import { router } from 'expo-router';
import React from 'react';

import { Text } from '@/components/ui/text';
import { useThemeColor } from '@/hooks/useThemeColor';
import { useTranslation } from '@/hooks/useTranslation';
import { ConfigType, configTypes } from '@/lib/types';
import { SafeAreaView, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';

export default function AddConfigScreen() {
  const { t } = useTranslation();
  const backgroundColor = useThemeColor({}, 'background');
  const borderColor = useThemeColor({}, 'border');
  const textColor = useThemeColor({}, 'text');

  const handleTypeSelect = (type: ConfigType) => {
    router.push({
      pathname: `/config-form/${type}`,
    })
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor }]}>
      <ScrollView style={styles.content}>
        <View style={styles.typeList}>
          {configTypes.map((configType) => (
            <View key={configType}>
              
              <TouchableOpacity
                style={styles.typeItem}
                onPress={() => handleTypeSelect(configType)}
              >
                <Text style={[styles.title, { color: textColor }]}>
                  {t(`configTypes.${configType}`)}
                </Text>
              </TouchableOpacity>
              <View style={[styles.separator, { backgroundColor: borderColor }]} />
            </View>
          ))}
        </View>
      </ScrollView>


    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor will be set dynamically
  },
  content: {
    flex: 1,
    padding: 0,
  },
  typeList: {
    flex: 1,
  },
  typeItem: {
    paddingVertical: 12,  // 参考3x-ui的12px垂直边距
    paddingHorizontal: 12, // 参考3x-ui的12px水平边距
    // backgroundColor will inherit from parent
  },
  title: {
    fontSize: 16,         // 参考3x-ui的16px字体大小
    fontWeight: '600',    // 参考3x-ui的600字体权重
  },
  separator: {
    height: 1,            // 参考3x-ui的1px分隔线高度
    // backgroundColor will be set dynamically
  },
});
